import Markdown from 'react-native-markdown-display';
import { useHistoricalPrices, useStockQuote } from '@/hooks/useFMP';
import CandleStickChart from '@/components/stock/candlestick-chart';
import { Message } from '@/utils/ai-chat/messageHelpers';
import { useStockStore } from '@/stores/stock';
import { isMarketOpen } from '@/utils/stock-market';
import { formatNumber } from '@/utils/format';
import AnimatedPrice from '@/components/common/AnimatedPrice';
import Skeleton from '@/components/common/Skeleteton';
import { Image } from 'react-native';

import React from 'react';

import { Platform, Text, View, ActivityIndicator } from 'react-native';

interface MessageContentProps {
  content: string;
  isUser: boolean;
  message?: Message; // Optional for backward compatibility
}

// Markdown styles
const markdownStyles = {
  body: {
    color: '#000000', // Black text for light background
    lineHeight: 20,
  },
  paragraph: {
    marginVertical: 8,
    lineHeight: 20,
    includeFontPadding: false,
  },
  text: {
    lineHeight: 20,
    includeFontPadding: false,
    selectable: true,
  },
  strong: {
    fontWeight: '700' as const,
    lineHeight: 20,
  },
  em: {
    fontStyle: 'italic' as const,
    lineHeight: 20,
  },
  link: {
    color: '#0066cc', // Blue links for light background
    lineHeight: 20,
  },
  list: {
    marginVertical: 4,
  },
  listItem: {
    marginVertical: 2,
    lineHeight: 20,
  },
  listItemText: {
    lineHeight: 20,
    includeFontPadding: false,
  },
  code_inline: {
    backgroundColor: 'rgba(0, 0, 0, 0.05)', // Light gray for code
    padding: 4,
    borderRadius: 4,
    fontFamily: Platform.select({
      ios: 'Menlo',
      android: 'monospace',
    }),
    lineHeight: 20,
  },
  code_block: {
    backgroundColor: 'rgba(0, 0, 0, 0.05)', // Light gray for code
    padding: 8,
    borderRadius: 4,
    fontFamily: Platform.select({
      ios: 'Menlo',
      android: 'monospace',
    }),
    lineHeight: 20,
  },
};

const MessageContent = ({ content, isUser, message }: MessageContentProps) => {
  // Process content to handle newlines and ensure consistent spacing
  const processContent = (text: string) => {
    return text
      .replace(/\\n/g, '\n')
      .replace(/\n\n+/g, '\n\n')
      .split('\n')
      .map(line => line.trim())
      .join('\n')
      .trim();
  };

  // Check if the content contains any markdown-like syntax
  const hasMarkdown = /[*#`\[\]\(\)\n]/.test(content);

  // Render text content
  const renderTextContent = (textContent: string) => {
    if (hasMarkdown) {
      return (
        <Markdown style={isUser ? { ...markdownStyles, body: { color: '#FFFFFF' } } : markdownStyles}>
          {processContent(textContent)}
        </Markdown>
      );
    }

    return (
      <Text
        selectable={true}
        style={{
          lineHeight: 20,
          includeFontPadding: false,
          color: isUser ? '#FFFFFF' : '#000000',
        }}>
        {processContent(textContent)
          .split('\n')
          .map((line, i, arr) => (
            <React.Fragment key={i}>
              {line}
              {i < arr.length - 1 && '\n'}
            </React.Fragment>
          ))}
      </Text>
    );
  };

  // Chart header component for displaying stock overview
  const ChartHeader = ({ symbol }: { symbol: string }) => {
    const stockStore = useStockStore();
    const stock = stockStore.getStock(symbol);
    const marketOpen = isMarketOpen();

    // Check if we should use store data
    const useStoreData = marketOpen && stock && stock.lastPrice > 0;

    // Fetch FMP quote when market is closed or store data is not available
    const { data: fmpQuote, isLoading: fmpLoading } = useStockQuote(symbol, {
      enabled: !useStoreData,
    });

    let currentPrice: number | undefined;
    let priceChange: number;
    let priceChangePercentage: number;
    let isLoading: boolean;
    let exchange = fmpQuote?.exchange || '';

    if (useStoreData) {
      // Use store data when market is open
      currentPrice = stock.lastPrice;
      priceChange = stock.lastClosePrice ? stock.lastPrice - stock.lastClosePrice : 0;
      priceChangePercentage = stock.lastClosePrice
        ? ((stock.lastPrice - stock.lastClosePrice) / stock.lastClosePrice) * 100
        : 0;
      isLoading = false;
    } else {
      // Use FMP data when market is closed or store data is not available
      currentPrice = fmpQuote?.price;
      priceChange = fmpQuote?.change || 0;
      priceChangePercentage = fmpQuote?.changesPercentage || 0;
      isLoading = fmpLoading || !fmpQuote;
    }

    return (
      <View style={{
        backgroundColor: '#f8f9fa',
        padding: 8,
        borderTopLeftRadius: 8,
        borderTopRightRadius: 8,
        borderBottomWidth: 1,
        borderBottomColor: '#e0e0e0',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <View className="flex-row items-center gap-2">
          <View className="h-12 w-12 items-center justify-center overflow-hidden rounded-lg">
            <Image
              className="h-full w-full"
              source={{
                uri: `https://financialmodelingprep.com/image-stock/${symbol}.png`,
              }}
            />
          </View>
          <View className="flex-col gap-1">
            <Text className="text-lg font-bold text-gray-900">
              {symbol}
            </Text>
            <View className="flex-row items-center gap-2">
            <Text className="text-xs  text-theme border border-theme rounded-sm p-0.5">
              {exchange}
            </Text>
            <Text className="text-xs  text-theme border border-theme rounded-sm p-0.5">
              Stock
            </Text>
              <Text className={`text-xs border rounded-sm p-0.5 ${isMarketOpen() ? 'text-green-500 border-green-500' : 'text-gray-500 border-gray-500'}`}>
                {isMarketOpen() ? 'Open' : 'Closed'}
              </Text>
            </View>
          </View>
        </View>

        {isLoading || !currentPrice ? (
          <View style={{ alignItems: 'flex-end', gap: 4 }}>
            <Skeleton className="h-5 w-16" />
            <Skeleton className="h-4 w-20" />
          </View>
        ) : (
          <View style={{ alignItems: 'flex-end' }}>
            <AnimatedPrice
              price={currentPrice}
              textStyle="text-lg font-bold text-gray-900"
              formatPrice={price => `$${formatNumber(price)}`}
              showCurrency={false}
            />
            <View style={{ flexDirection: 'row', alignItems: 'center', gap: 4, marginTop: 2 }}>
              <Text
                style={{
                  fontSize: 12,
                  fontWeight: '600',
                  color: priceChangePercentage >= 0 ? '#16a34a' : '#dc2626'
                }}>
                {priceChangePercentage >= 0 ? '↑' : '↓'} {Math.abs(priceChangePercentage).toFixed(2)}%
              </Text>
              <Text style={{
                fontSize: 12,
                color: priceChange >= 0 ? '#16a34a' : '#dc2626'
              }}>
                ({priceChange >= 0 ? '+' : '-'}${Math.abs(priceChange).toFixed(2)})
              </Text>
            </View>
          </View>
        )}
      </View>
    );
  };

  // Chart component for rendering chart content blocks
  const ChartComponent = ({ symbol }: { symbol: string }) => {
    const { data: historicalData, isLoading, error } = useHistoricalPrices(symbol);

    if (isLoading) {
      return (
        <View style={{ height: 300, justifyContent: 'center', alignItems: 'center', backgroundColor: '#f5f5f5', borderRadius: 8 }}>
          <ActivityIndicator size="large" color="#FF9339" />
          <Text style={{ marginTop: 10, color: '#666' }}>Loading chart for {symbol}...</Text>
        </View>
      );
    }

    if (error || !historicalData) {
      return (
        <View style={{ height: 200, justifyContent: 'center', alignItems: 'center', backgroundColor: '#f5f5f5', borderRadius: 8 }}>
          <Text style={{ color: '#666', textAlign: 'center' }}>
            Failed to load chart data for {symbol}
          </Text>
        </View>
      );
    }

    return (
      <View style={{ marginVertical: 10, borderRadius: 8, overflow: 'hidden', backgroundColor: '#ffffff', borderWidth: 1, borderColor: '#e0e0e0' }}>
        <ChartHeader symbol={symbol} />
        <CandleStickChart data={historicalData} height={400} />
      </View>
    );
  };

  // If message has content blocks, render them
  if (message?.contentBlocks && message.contentBlocks.length > 0) {
    return (
      <View>
        {/* Render main text content first */}
        {content && renderTextContent(content)}

        {/* Render content blocks */}
        {message.contentBlocks.map((block, index) => {
          if (block.type === 'chart') {
            return (
              <ChartComponent key={index} symbol={block.content.symbol} />
            );
          }

          if (block.type === 'message') {
            return (
              <View key={index} style={{ marginTop: content ? 10 : 0 }}>
                {renderTextContent(block.content)}
              </View>
            );
          }

          return null;
        })}
      </View>
    );
  }

  // Default behavior - render text content only
  return renderTextContent(content);
};

export default MessageContent;
