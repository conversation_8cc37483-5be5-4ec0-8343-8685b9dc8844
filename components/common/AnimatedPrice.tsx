import { useEffect, useRef, useState } from 'react';

import { Animated, Text, View } from 'react-native';

type AnimatedPriceProps = {
  price: number;
  className?: string;
  textStyle?: string;
  formatPrice?: (price: number) => string;
  showCurrency?: boolean;
};

export default function AnimatedPrice({
  price,
  className = '',
  textStyle = 'font-bold text-zinc-900',
  formatPrice,
  showCurrency = true,
}: AnimatedPriceProps) {
  // Animation and price tracking state
  const [previousPrice, setPreviousPrice] = useState<number | null>(null);
  const [priceDirection, setPriceDirection] = useState<'up' | 'down' | null>(null);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;

  // Trigger animation when price changes
  useEffect(() => {
    if (price > 0 && previousPrice !== null && previousPrice !== price) {
      const direction = price > previousPrice ? 'up' : 'down';
      setPriceDirection(direction);

      // Reset animations
      fadeAnim.setValue(0.5);
      scaleAnim.setValue(1.05);

      // Start fade out and scale animation
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 1500,
          useNativeDriver: false,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start(() => {
        setPriceDirection(null);
      });
    }

    if (price > 0) {
      setPreviousPrice(price);
    }
  }, [price, previousPrice, fadeAnim, scaleAnim]);

  const formatPriceDisplay = (price: number): string => {
    if (formatPrice) {
      return formatPrice(price);
    }
    return showCurrency ? `${price.toFixed(2)}` : price.toFixed(2);
  };

  return (
    <View className={`relative ${className}`}>
      <Animated.View
        style={{
          transform: [{ scale: scaleAnim }],
        }}>
        <Text className={textStyle}>{formatPriceDisplay(price)}</Text>
      </Animated.View>

      {/* Colorful fade out overlay */}
      {priceDirection && (
        <Animated.View
          className="absolute inset-0 rounded-md"
          style={{
            backgroundColor:
              priceDirection === 'up'
                ? 'rgba(34, 197, 94, 0.6)' // Light green
                : 'rgba(239, 68, 68, 0.6)', // Light red
            opacity: fadeAnim,
          }}
        />
      )}
    </View>
  );
}
