import { ApiResponseParams } from '@/types/api';
import { UserFeedbackRequest, UserFeedbackResponse } from '@/types/api/user';

import { apiClient, handleApiError } from './client';
import { API_PATHS } from './paths';

export const userService = {
  async submitFeedback(feedbackData: UserFeedbackRequest): Promise<UserFeedbackResponse> {
    try {
      const { data } = await apiClient.post<ApiResponseParams<UserFeedbackResponse>>(
        API_PATHS.User.feedback,
        feedbackData
      );

      if (data.success) {
        return {
          success: true,
          message: data.message,
        };
      } else {
        throw new Error(data.message || 'Failed to submit feedback');
      }
    } catch (error) {
      throw handleApiError(error);
    }
  },
};
