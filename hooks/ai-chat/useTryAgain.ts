import { makeApiCall, processStreamingResponse } from '@/utils/ai-chat/apiHelpers';
import {
  Message,
  createNewResponseSlot,
  updateCurrentResponseSlot,
  findCorrespondingUserMessage,
} from '@/utils/ai-chat/messageHelpers';
import { useTranslation } from 'react-i18next';

import { useRef } from 'react';

import { useAuth } from '@/hooks/useAuth';

export const useTryAgain = (
  messages: Message[],
  setMessages: React.Dispatch<React.SetStateAction<Message[]>>,
  currentChatId: string | null,
  dailyUsageCount: number,
  dailyUsageLimit: number,
  usageData: any,
  chatDetailsData: any,
  setIsLoading: (loading: boolean) => void,
  setIsTryingAgain: (trying: boolean) => void,
  setRetryingMessageIndex: (index: number | null) => void,
  setStreamingMessage: (message: string) => void,
  setIsRequestActive: (active: boolean) => void,
  refetchUsage: () => void,
  refetchChatDetails: () => void
) => {
  const { t } = useTranslation();
  const { user } = useAuth();

  // AbortController for cancelling ongoing API requests
  const abortControllerRef = useRef<AbortController | null>(null);

  const handleTryAgain = async (index: number, showToast: (message: string) => void) => {
    // Get the assistant message
    const assistantMessage = messages[index];
    if (assistantMessage.role !== 'assistant') {
      console.error('Try again can only be called on assistant messages');
      return;
    }

    // Cancel any existing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new AbortController for this request
    const abortController = new AbortController();
    abortControllerRef.current = abortController;
    setIsRequestActive(true);

    // Get the user message ID from chat details using the assistant message's AI message ID
    let retryMessageId: string | null = null;

    if (chatDetailsData?.data?.messages && assistantMessage.aiMessageId) {
      // Find the AI message in chat details that matches our assistant message
      const chatMessages = chatDetailsData.data.messages;
      const aiMessage = chatMessages.find(
        (msg: any) => msg.role === 'AI' && msg.id === assistantMessage.aiMessageId
      );

      if (aiMessage && aiMessage.parentId) {
        retryMessageId = aiMessage.parentId;
      }
    }

    // If we don't have chat details or can't find the message ID, fall back to the old method
    if (!retryMessageId) {
      const userMessageData = findCorrespondingUserMessage(messages, index);
      if (!userMessageData) {
        console.error('No corresponding user message found for retry');
        setIsRequestActive(false);
        abortControllerRef.current = null;
        return;
      }
    }

    // Check daily usage limit before making API call
    if (!user?.email) {
      showToast(t('aiChat.messages.authError'));
      setIsRequestActive(false);
      abortControllerRef.current = null;
      return;
    }

    // Use current usage from API data instead of local storage
    const currentUsage = dailyUsageCount;
    const userDailyLimit = dailyUsageLimit;

    // Check against the user's actual limit
    if (currentUsage >= userDailyLimit) {
      // Calculate hours until reset from API data
      const resetDateTime = usageData?.data?.resetDateTime;
      let hoursUntilReset = 24; // Default fallback

      if (resetDateTime) {
        const resetTime = new Date(resetDateTime);
        const now = new Date();
        const diffInMs = resetTime.getTime() - now.getTime();
        hoursUntilReset = Math.max(1, Math.ceil(diffInMs / (1000 * 60 * 60))); // At least 1 hour
      }

      const errorMessage = t('aiChat.messages.limitReached', {
        limit: userDailyLimit,
        current: currentUsage,
        hours: hoursUntilReset,
      });

      // Update the current response slot with error message (no follow-up questions for errors)
      updateCurrentResponseSlot(setMessages, index, errorMessage, undefined, false);
      showToast(
        t('aiChat.messages.limitReachedShort', { current: currentUsage, limit: userDailyLimit })
      );
      setIsRequestActive(false);
      abortControllerRef.current = null;
      return;
    }

    // Create a new response slot immediately for the retry
    createNewResponseSlot(setMessages, index, '');

    // Set loading and try again states
    setIsLoading(true);
    setIsTryingAgain(true);
    setRetryingMessageIndex(index);
    setStreamingMessage('');

    try {
      // Call API with retry message ID if available, otherwise use fallback user message
      let response;
      if (retryMessageId) {
        response = await makeApiCall(
          undefined,
          currentChatId || undefined,
          retryMessageId,
          abortController.signal
        );
      } else {
        const userMessageData = findCorrespondingUserMessage(messages, index);
        if (!userMessageData) {
          throw new Error('No user message found for retry');
        }
        response = await makeApiCall(
          userMessageData.message,
          currentChatId || undefined,
          undefined,
          abortController.signal
        );
      }

      // Check if request was aborted
      if (abortController.signal.aborted) {
        console.log('Retry request was aborted');
        return;
      }

      // API will handle usage increment server-side, so we just need to refetch usage
      refetchUsage();

      // Show usage info occasionally - calculate expected usage after this API call
      setTimeout(() => {
        // The API call we just made will increment usage by 1
        const expectedUsage = currentUsage + 1;
        if (expectedUsage >= userDailyLimit - 5 && expectedUsage < userDailyLimit) {
          showToast(
            t('aiChat.messages.usesRemaining', { remaining: userDailyLimit - expectedUsage })
          );
        }
      }, 500);

      // Custom streaming handler that updates both the response slot and streaming message
      const { content, metadata } = await processStreamingResponse(
        response,
        content => {
          // Update the current response slot with streaming content
          updateCurrentResponseSlot(setMessages, index, content);
          // Also update streamingMessage so cancel button appears
          setStreamingMessage(content);
        },
        abortController.signal
      );

      // Check if request was aborted after processing
      if (abortController.signal.aborted) {
        console.log('Retry request was aborted during processing');
        // The response slot was already created and updated during streaming, so the content is preserved
        return;
      }

      if (content) {
        // Update the current response slot that was created when retry started
        updateCurrentResponseSlot(setMessages, index, content, metadata?.aiMessageId);

        // Refetch history and chat details to ensure retry responses persist when switching chats
        if (currentChatId) {
          refetchChatDetails();
        }
      }
    } catch (error) {
      // Check if error is due to abort
      if (error instanceof Error && error.name === 'AbortError') {
        console.log('Retry request was aborted');
        // Don't show error message for user-initiated cancellation
        // The response slot was already created and may have partial content
        return;
      }

      console.error('Error:', error);
      // Update the current response slot with error message (slot was already created, no follow-up questions for errors)
      if (error instanceof Error && error.message === 'User not authenticated') {
        updateCurrentResponseSlot(
          setMessages,
          index,
          t('aiChat.messages.authError'),
          undefined,
          false
        );
      } else {
        updateCurrentResponseSlot(setMessages, index, t('aiChat.messages.error'), undefined, false);
      }
    } finally {
      // Clean up request state
      setIsRequestActive(false);
      if (abortControllerRef.current === abortController) {
        abortControllerRef.current = null;
      }
      setIsLoading(false);
      setIsTryingAgain(false);
      setRetryingMessageIndex(null);
      setStreamingMessage('');
    }
  };

  const cancelTryAgain = () => {
    if (abortControllerRef.current) {
      console.log('User cancelled try again request');
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
      setIsRequestActive(false);
      setIsLoading(false);
      setStreamingMessage('');
      setIsTryingAgain(false);
      setRetryingMessageIndex(null);
    }
  };

  return {
    handleTryAgain,
    cancelTryAgain,
  };
};
