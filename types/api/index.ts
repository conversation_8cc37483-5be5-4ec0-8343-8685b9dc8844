export type ApiResponseParams<T> = {
  success: boolean;
  data?: T;
  message?: string;
  translationKey?: string;
};

// Re-export all API types
export * from './user';
export * from './ai-chat';
export * from './auth';
export * from './fmp';
export * from './report';
export * from './transformer';

// Export stock types explicitly to avoid CompanySearchResponse conflict with fmp
export type {
  AnalysisStockTrendResponse,
  AnalysisStockAndIndustryComparisonResponse,
  ResearcherFinancialStatementResponse,
  ResearcherTargetGradingResponse,
  ResearcherStockNewsResponse,
  UserViewStockUsageResponse,
  LatestClosingPricesResponse,
} from './stock';

// Note: CompanySearchResponse is excluded due to conflict with fmp module
