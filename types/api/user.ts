// User API Types

// Feedback types based on the provided schema
export type FeedbackType = 'like' | 'dislike' | 'report' | 'suggestion';

export type UserFeedbackRequest = {
  feature: string;
  feedback_type: FeedbackType;
  reasons?: string[];
  comments?: string | null;
  rating?: number | null;
  metadata?: Record<string, any> | null;
};

export type UserFeedbackResponse = {
  success: boolean;
  message?: string;
};
