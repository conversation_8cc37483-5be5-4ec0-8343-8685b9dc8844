import { I18n<PERSON>rovider } from '@/i18n/provider';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { PortalProvider } from '@gorhom/portal';
import { QueryClientProvider } from '@tanstack/react-query';
import * as Notifications from 'expo-notifications';
import { router, Stack } from 'expo-router';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { KeyboardProvider } from 'react-native-keyboard-controller';

import { useEffect, useRef, useState } from 'react';

import { Platform } from 'react-native';
import { Text } from 'react-native';

import { TutorialProvider } from '@/components/tutorial/TutorialContext';

import { queryClient } from '../lib/query/queryClient';

import '../global.css';

// Note: Notification handler is now configured in the notification service
// This ensures consistent badge behavior across the app

// Define a type for pending notification navigation
type PendingNavigation = {
  route: string;
  params?: Record<string, any>;
};

export default function RootLayout() {
  const notificationListener = useRef<any>();
  const responseListener = useRef<any>();
  const [pendingNavigation, setPendingNavigation] = useState<PendingNavigation | null>(null);
  const [isAppReady, setIsAppReady] = useState(false);

  // Handle navigation after app is mounted
  useEffect(() => {
    if (isAppReady && pendingNavigation) {
      // Add a small delay to ensure navigation is fully ready
      const timer = setTimeout(() => {
        try {
          console.log(`Navigating to: ${pendingNavigation.route}`);
          router.push(pendingNavigation.route as any);
          setPendingNavigation(null);
        } catch (error) {
          console.error('Navigation error:', error);
        }
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [isAppReady, pendingNavigation]);

  // Set app as ready after initial render
  useEffect(() => {
    setIsAppReady(true);

    // Check if app was opened from a notification
    const checkInitialNotification = async () => {
      try {
        const initialNotification = await Notifications.getLastNotificationResponseAsync();
        if (initialNotification) {
          console.log('App opened from notification:', initialNotification);

          const data = initialNotification.notification.request.content.data;
          if (!data) {
            console.log('No data in initial notification');
            return;
          }

          // Set pending navigation based on notification type
          if (data.type === 'weekly_report') {
            setPendingNavigation({ route: '/(tabs)/report' });
          } else if (data.type === 'potential_stocks') {
            setPendingNavigation({ route: '/(tabs)/home' });
          } else if (data.type === 'preferred_stocks') {
            setPendingNavigation({ route: '/(tabs)/home' });
          } else {
            console.log('Unknown notification type:', data.type);
            // Default to home tab if type is unknown
            setPendingNavigation({ route: '/(tabs)/home' });
          }
        }
      } catch (error) {
        console.error('Error checking initial notification:', error);
      }
    };

    checkInitialNotification();
  }, []);

  useEffect(() => {
    // Request notification permissions
    const requestPermissions = async () => {
      try {
        const { status: existingStatus } = await Notifications.getPermissionsAsync();
        let finalStatus = existingStatus;

        if (existingStatus !== 'granted') {
          const { status } = await Notifications.requestPermissionsAsync();
          finalStatus = status;
        }

        if (finalStatus !== 'granted') {
          console.log('Permission not granted for notifications');
          return false;
        }

        // For Android, create a notification channel
        if (Platform.OS === 'android') {
          Notifications.setNotificationChannelAsync('reports', {
            name: 'Report Notifications',
            importance: Notifications.AndroidImportance.HIGH,
            vibrationPattern: [0, 250, 250, 250],
            lightColor: '#FF6B00',
          });
        }

        return true;
      } catch (error) {
        console.error('Error requesting notification permissions:', error);
        return false;
      }
    };

    requestPermissions();

    // Add notification received listener
    notificationListener.current = Notifications.addNotificationReceivedListener(notification => {
      console.log('Notification received!', notification);
    });

    // Add notification response listener (when user taps on notification)
    responseListener.current = Notifications.addNotificationResponseReceivedListener(response => {
      console.log('Notification response received!', response);

      try {
        const data = response.notification.request.content.data;

        if (!data) {
          console.log('No data in notification');
          return;
        }

        // Instead of navigating immediately, set the pending navigation
        if (data.type === 'weekly_report') {
          setPendingNavigation({ route: '/(tabs)/report' });
        } else if (data.type === 'potential_stocks') {
          setPendingNavigation({ route: '/(tabs)/home' });
        } else if (data.type === 'preferred_stocks') {
          setPendingNavigation({ route: '/(tabs)/home' });
        } else {
          console.log('Unknown notification type:', data.type);
          // Default to home tab if type is unknown
          setPendingNavigation({ route: '/(tabs)/home' });
        }
      } catch (error) {
        console.error('Error handling notification response:', error);
      }
    });

    return () => {
      // Clean up listeners
      if (notificationListener.current) {
        Notifications.removeNotificationSubscription(notificationListener.current);
      }
      if (responseListener.current) {
        Notifications.removeNotificationSubscription(responseListener.current);
      }
    };
  }, []);

  return (
    <TutorialProvider>
      <QueryClientProvider client={queryClient}>
        <I18nProvider>
          <KeyboardProvider>
            <GestureHandlerRootView style={{ flex: 1 }}>
              <BottomSheetModalProvider>
                <PortalProvider>
                  {(() => {
                    try {
                      const {
                        ReportNotificationInitializer,
                      } = require('@/components/common/ReportNotificationInitializer');
                      return <ReportNotificationInitializer />;
                    } catch (error) {
                      console.log('Error loading notification component:', error);
                      return null;
                    }
                  })()}
                  {(() => {
                    try {
                      const {
                        NotificationBadgeManager,
                      } = require('@/components/common/NotificationBadgeManager');
                      return <NotificationBadgeManager />;
                    } catch (error) {
                      console.log('Error loading badge manager component:', error);
                      return null;
                    }
                  })()}
                  {(() => {
                    try {
                      const AnnouncementManager =
                        require('@/components/announcement/AnnouncementManager').default;
                      return <AnnouncementManager requireAuthentication={true} delay={3000} />;
                    } catch (error) {
                      console.log('Error loading announcement manager:', error);
                      return null;
                    }
                  })()}
                  <Stack>
                    <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
                    <Stack.Screen
                      name="stock/[symbol]"
                      options={{ headerShown: true, headerShadowVisible: false }}
                    />
                    <Stack.Screen name="index" options={{ headerShown: false }} />
                    <Stack.Screen name="setting/ai-chat" options={{ headerShown: false }} />
                    <Stack.Screen
                      name="onboarding/index"
                      options={{
                        title: 'Onboarding',
                        headerShown: false,
                      }}
                    />
                  </Stack>
                </PortalProvider>
              </BottomSheetModalProvider>
            </GestureHandlerRootView>
          </KeyboardProvider>
        </I18nProvider>
      </QueryClientProvider>
    </TutorialProvider>
  );
}
