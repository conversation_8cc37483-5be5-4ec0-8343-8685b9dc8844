import { formatNumber } from '@/utils/format';
import * as Haptics from 'expo-haptics';
import { Stack, useLocalSearchParams } from 'expo-router';
import { useTranslation } from 'react-i18next';

import { useState } from 'react';

import { View, Text, SafeAreaView, FlatList, Dimensions, TouchableOpacity } from 'react-native';

import { useHistoricalPrices } from '@/hooks/useFMP';
import { useViewStockUsage } from '@/hooks/useStock';

import ExceedLimitView from '@/components/common/ExceedLimitView';
import { PageDescription } from '@/components/common/PageDescription';
import { SectionHeader } from '@/components/common/SectionHeader';
import TradingViewChart from '@/components/stock/candlestick-chart';
import StockLoading from '@/components/stock/stock-loading';

export default function Chart() {
  const { t } = useTranslation();
  const { symbol } = useLocalSearchParams<{ symbol: string }>();
  const { data: historicalData, isLoading: isLoadingHistorical } = useHistoricalPrices(symbol);
  const { data: stockUsage } = useViewStockUsage();

  const screenHeight = Dimensions.get('window').height;
  // Use a good portion of the screen height for the chart
  const chartHeight = screenHeight * 0.6;

  // Time range selection state
  const [selectedTimeRange, setSelectedTimeRange] = useState('3M');

  // Check if limit reached
  const hasReachedLimit = (stockUsage?.data?.usage ?? 0) >= (stockUsage?.data?.limit ?? 0);
  const isStockViewed = stockUsage?.data?.stocks?.includes(symbol) ?? false;

  if (hasReachedLimit && !isStockViewed) {
    return <ExceedLimitView />;
  }

  if (isLoadingHistorical) {
    return <StockLoading />;
  }

  if (!historicalData?.historical?.length) {
    return (
      <View className="flex-1 items-center justify-center">
        <Text className="text-lg text-gray-600">{t('common.noData')}</Text>
      </View>
    );
  }

  // Filter data based on selected time range for analysis only
  const filterDataByTimeRange = (data: any[], range: string) => {
    if (!data || data.length === 0) return data;

    const days =
      {
        '1M': 30,
        '3M': 90,
        '6M': 180,
        '1Y': 365,
      }[range] || 90;

    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);

    return data.filter(item => new Date(item.date) >= cutoffDate);
  };

  // Filter data only for analysis calculations
  const filteredDataForAnalysis = filterDataByTimeRange(
    historicalData.historical,
    selectedTimeRange
  );

  // Calculate chart statistics based on filtered data
  const prices = filteredDataForAnalysis.map(item => parseFloat(item.close.toString()));
  const volumes = filteredDataForAnalysis.map(item => parseFloat(item.volume.toString()));
  const maxPrice = Math.max(...prices);
  const minPrice = Math.min(...prices);
  const avgVolume = volumes.reduce((a, b) => a + b, 0) / volumes.length;
  const priceRange = maxPrice - minPrice;
  const priceRangePercent = (priceRange / minPrice) * 100;

  const chartAnalysis = {
    priceRange: `$${formatNumber(minPrice)} - $${formatNumber(maxPrice)}`,
    priceRangePercent: `${formatNumber(priceRangePercent)}%`,
    avgVolume: formatNumber(avgVolume, 0),
    dataPoints: filteredDataForAnalysis.length,
    timeRange: selectedTimeRange,
  };

  const handleTimeRangeChange = (range: string) => {
    // Add haptic feedback for time range change
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setSelectedTimeRange(range);
  };

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <FlatList
        ListHeaderComponent={<PageDescription description={t('stock.chart.description')} />}
        data={[{ historical: historicalData.historical }]} // Pass full data
        contentContainerClassName="px-4 py-4"
        keyExtractor={(item, index) => index.toString()}
        renderItem={({ item }) => (
          <View>
            <Stack.Screen
              options={{
                headerShown: false,
              }}
            />

            {/* Interactive Chart Card */}
            <View className="mb-4 overflow-hidden rounded-2xl bg-white shadow-sm">
              <SectionHeader
                label={t('stock.chart.interactive.label')}
                title={t('stock.chart.interactive.title')}
                icon="line-chart"
              />

              {/* Time Range Selection */}
              <View className="flex-row justify-center gap-2 border-b border-gray-100 px-4 py-3">
                {['1M', '3M', '6M', '1Y'].map(range => (
                  <TouchableOpacity
                    key={range}
                    style={{
                      paddingHorizontal: 12,
                      paddingVertical: 6,
                      borderRadius: 6,
                      backgroundColor: selectedTimeRange === range ? '#FF6B00' : '#F3F4F6',
                    }}
                    onPress={() => handleTimeRangeChange(range)}>
                    <Text
                      style={{
                        fontSize: 12,
                        color: selectedTimeRange === range ? 'white' : '#6B7280',
                        fontWeight: selectedTimeRange === range ? '600' : '400',
                      }}>
                      {range}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>

              <View className="p-2">
                <TradingViewChart
                  data={{ symbol, historical: item.historical }} // Full data
                  height={chartHeight}
                  timeRange={selectedTimeRange} // Pass time range to chart
                />
              </View>
            </View>

            {/* Chart Analysis Card */}
            <View className="mb-4 overflow-hidden rounded-2xl bg-white shadow-sm">
              <SectionHeader
                label={t('stock.chart.analysis.label')}
                title={`${t('stock.chart.analysis.title')} (${chartAnalysis.timeRange})`}
                icon="bar-chart"
              />
              <View className="p-5">
                <View className="flex-row flex-wrap justify-between">
                  <View className="mb-3 w-[48%] rounded-lg bg-orange-50 p-3">
                    <Text className="text-sm text-orange-500">
                      {t('stock.chart.analysis.priceRange')}
                    </Text>
                    <Text className="font-semibold text-gray-800">{chartAnalysis.priceRange}</Text>
                  </View>
                  <View className="mb-3 w-[48%] rounded-lg bg-orange-50 p-3">
                    <Text className="text-sm text-orange-500">
                      {t('stock.chart.analysis.volatility')}
                    </Text>
                    <Text className="font-semibold text-gray-800">
                      {chartAnalysis.priceRangePercent}
                    </Text>
                  </View>
                  <View className="mb-3 w-[48%] rounded-lg bg-orange-50 p-3">
                    <Text className="text-sm text-orange-500">
                      {t('stock.chart.analysis.avgVolume')}
                    </Text>
                    <Text className="font-semibold text-gray-800">{chartAnalysis.avgVolume}</Text>
                  </View>
                  <View className="mb-3 w-[48%] rounded-lg bg-orange-50 p-3">
                    <Text className="text-sm text-orange-500">
                      {t('stock.chart.analysis.dataPoints')}
                    </Text>
                    <Text className="font-semibold text-gray-800">{chartAnalysis.dataPoints}</Text>
                  </View>
                </View>
              </View>
            </View>
          </View>
        )}
      />
    </SafeAreaView>
  );
}
